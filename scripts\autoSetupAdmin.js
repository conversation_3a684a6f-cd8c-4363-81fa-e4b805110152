import admin from 'firebase-admin';

// Initialize Firebase Admin SDK
const serviceAccount = {
  "type": "service_account",
  "project_id": "s3kt0r-30ede",
  "private_key_id": process.env.FIREBASE_PRIVATE_KEY_ID,
  "private_key": process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  "client_email": process.env.FIREBASE_CLIENT_EMAIL,
  "client_id": process.env.FIREBASE_CLIENT_ID,
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
};

// Fallback configuration for local development
const firebaseConfig = {
  apiKey: "AIzaSyBz12g2Ilty-HnKNCLq4rD9q0bMivjcGUM",
  authDomain: "s3kt0r-30ede.firebaseapp.com",
  projectId: "s3kt0r-30ede",
  storageBucket: "s3kt0r-30ede.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:4c7a0f2660fcc9880af3c2"
};

async function setupAdmin() {
  try {
    // Try to initialize with service account, fallback to project ID only
    let app;
    try {
      if (process.env.FIREBASE_PRIVATE_KEY) {
        app = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: "s3kt0r-30ede"
        });
      } else {
        app = admin.initializeApp({
          projectId: "s3kt0r-30ede"
        });
      }
    } catch (error) {
      console.log('Using default Firebase project configuration...');
      app = admin.initializeApp({
        projectId: "s3kt0r-30ede"
      });
    }

    const auth = admin.auth();
    const db = admin.firestore();

    console.log('🔥 Setting up admin account...');

    // Create admin user
    const adminEmail = '<EMAIL>';
    const adminPassword = 'Tr1p$t0p3301';

    let adminUser;
    try {
      // Try to get existing user
      adminUser = await auth.getUserByEmail(adminEmail);
      console.log('✅ Admin user already exists:', adminUser.uid);
    } catch (error) {
      // Create new admin user
      adminUser = await auth.createUser({
        email: adminEmail,
        password: adminPassword,
        emailVerified: true,
        disabled: false
      });
      console.log('✅ Created new admin user:', adminUser.uid);
    }

    // Create/update admin user document in Firestore
    const adminUserDoc = {
      id: adminUser.uid,
      username: 'S3Kt0R_Admin',
      avatarUrl: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=100&h=100&fit=crop&crop=face',
      isActive: true,
      isPendingApproval: false,
      isAdmin: true,
      bio: 'System Administrator\nThe truth is in the code\nChoose your path wisely\n#S3Kt0R #Truth #Code'
    };

    await db.collection('users').doc(adminUser.uid).set(adminUserDoc, { merge: true });
    console.log('✅ Admin user document created/updated in Firestore');

    // Initialize login screen config
    const loginConfig = {
      message: "Welcome to S3Kt0R-Gram. The truth is in the code. Choose your path.",
      imageUrl: "https://images.unsplash.com/photo-1505682499293-230dd9df9655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
      imageOverlayOpacity: 0.5
    };

    await db.collection('config').doc('loginScreen').set(loginConfig, { merge: true });
    console.log('✅ Login screen config initialized');

    // Create placeholder documents for collections
    await db.collection('posts').doc('_placeholder').set({
      _placeholder: true,
      createdAt: new Date().toISOString()
    }, { merge: true });

    await db.collection('adminMessages').doc('_placeholder').set({
      _placeholder: true,
      createdAt: new Date().toISOString()
    }, { merge: true });

    console.log('✅ Placeholder documents created');

    console.log('\n🎉 Admin setup complete!');
    console.log('📧 Admin Email:', adminEmail);
    console.log('🔑 Admin Password:', adminPassword);
    console.log('🆔 Admin UID:', adminUser.uid);
    console.log('\n🚀 Your Firebase setup is ready for S3Kt0R-Gram!');

  } catch (error) {
    console.error('❌ Error setting up admin:', error);
    process.exit(1);
  }
}

setupAdmin();
