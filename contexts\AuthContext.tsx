
import React, { createContext, useState, ReactNode, useMemo, useCallback, useEffect } from 'react';
import { User, LoginScreenConfig, AdminMessage } from '../types';
import { DEFAULT_LOGIN_SCREEN_CONFIG } from '../constants';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';
import { auth } from '../firebase';
import {
  getUserById,
  signIn,
  signUp as firebaseSignUp,
  logOut,
  getAllUsers,
  getActiveUsers,
  updateUser,
  getLoginScreenConfig,
  updateLoginScreenConfig as firebaseUpdateLoginScreenConfig,
  getAllAdminMessages,
  createAdminMessage,
  markAdminMessageAsRead,
  deleteAdminMessage as firebaseDeleteAdminMessage
} from '../services/firebaseService';

interface AuthContextType {
  currentUser: User | null;
  users: User[];
  isAdmin: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  signUpUser: (email: string, password: string, username: string) => Promise<User | null>;
  approveUser: (userId: string) => void;
  toggleUserActivation: (userId: string) => void;
  removeUser: (userId: string) => void;
  loginScreenConfig: LoginScreenConfig;
  updateLoginScreenConfig: (newConfig: Partial<LoginScreenConfig>) => void;
  adminMessages: AdminMessage[];
  sendMessageToAdmin: (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>) => Promise<void>;
  markMessageAsRead: (messageId: string) => void;
  deleteAdminMessage: (messageId: string) => void;
  updateUserProfile: (userId: string, updates: { bio?: string; avatarUrl?: string }) => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSigningUp, setIsSigningUp] = useState(false); // Track signup state

  const [loginScreenConfig, setLoginScreenConfig] = useState<LoginScreenConfig>(DEFAULT_LOGIN_SCREEN_CONFIG);

  const [adminMessages, setAdminMessages] = useState<AdminMessage[]>([]);

  const isAdmin = useMemo(() => currentUser?.isAdmin === true, [currentUser]);

  // Firebase auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        try {
          const userData = await getUserById(firebaseUser.uid);

          // If we're in signup flow, don't auto-sign out pending users
          if (isSigningUp && userData && !userData.isActive && userData.isPendingApproval) {
            // During signup, just clear the current user but don't sign out
            setCurrentUser(null);
            setIsSigningUp(false); // Reset signup flag
            return;
          }

          // Check if user is approved (normal login flow)
          if (userData && !userData.isActive && userData.isPendingApproval) {
            // User is not approved, sign them out and don't set as current user
            await logOut();
            setCurrentUser(null);
          } else if (userData && !userData.isActive && !userData.isPendingApproval) {
            // User account is deactivated, sign them out
            await logOut();
            setCurrentUser(null);
          } else {
            // User is approved or admin
            setCurrentUser(userData);
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          setCurrentUser(null);
        }
      } else {
        setCurrentUser(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, [isSigningUp]);

  // Load login screen config
  useEffect(() => {
    const loadLoginScreenConfig = async () => {
      try {
        const config = await getLoginScreenConfig();
        if (config) {
          setLoginScreenConfig(config);
        }
      } catch (error) {
        console.error('Error loading login screen config:', error);
      }
    };
    loadLoginScreenConfig();
  }, []);

  // Load users for all authenticated users, admin messages for admin only
  useEffect(() => {
    if (currentUser) {
      const loadUserData = async () => {
        try {
          if (isAdmin) {
            // Admin can see all users and admin messages
            const [allUsers, messages] = await Promise.all([
              getAllUsers(),
              getAllAdminMessages()
            ]);
            setUsers(allUsers);
            setAdminMessages(messages);
          } else {
            // Regular users can see active users only
            const activeUsers = await getActiveUsers();
            setUsers(activeUsers);
          }
        } catch (error) {
          console.error('Error loading user data:', error);
        }
      };
      loadUserData();
    } else {
      // Clear users when not authenticated
      setUsers([]);
      setAdminMessages([]);
    }
  }, [currentUser, isAdmin]);

  const login = useCallback(async (email: string, password: string): Promise<void> => {
    try {
      await signIn(email, password);
      // The auth state listener will handle setting the current user
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      await logOut();
      // The auth state listener will handle clearing the current user
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }, []);

  const signUpUser = useCallback(async (email: string, password: string, username: string): Promise<User | null> => {
    try {
      // Set signup flag to prevent auto-signout during signup
      setIsSigningUp(true);

      const newUser = await firebaseSignUp(email, password, username);

      // Refresh users list for admin
      if (isAdmin) {
        const allUsers = await getAllUsers();
        setUsers(allUsers);
      }

      return newUser;
    } catch (error) {
      console.error('Sign up error:', error);
      setIsSigningUp(false); // Reset flag on error
      throw error;
    }
  }, [isAdmin]);

  const approveUser = useCallback(async (userId: string) => { // Make it async
    try {
      await updateUser(userId, { isActive: true, isPendingApproval: false }); // Persist to Firebase
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId ? { ...user, isActive: true, isPendingApproval: false } : user
        )
      );
    } catch (error) {
      console.error('Error approving user:', error);
      throw error;
    }
  }, []);

  const toggleUserActivation = useCallback(async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId);
      if (!user) return;

      // Prevent deactivating admin accounts
      if (user.isAdmin) {
        alert("Cannot deactivate admin accounts.");
        return;
      }

      await updateUser(userId, {
        isActive: !user.isActive,
        isPendingApproval: user.isActive ? false : user.isPendingApproval
      });

      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === userId ? { ...u, isActive: !u.isActive, isPendingApproval: u.isActive ? false : u.isPendingApproval } : u
        )
      );

      if (currentUser?.id === userId && !user.isActive) {
        await logout();
      }
    } catch (error) {
      console.error('Error toggling user activation:', error);
      throw error;
    }
  }, [currentUser, users, logout]);

  const removeUser = useCallback(async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId);
      if (!user) return;

      // Prevent removing admin accounts
      if (user.isAdmin) {
        alert("Cannot remove admin accounts.");
        return;
      }

      // Note: In a real app, you'd want to delete the Firebase Auth user too
      // For now, we'll just remove from Firestore
      setUsers(prevUsers => prevUsers.filter(u => u.id !== userId));

      if (currentUser?.id === userId) {
        await logout();
      }
    } catch (error) {
      console.error('Error removing user:', error);
      throw error;
    }
  }, [currentUser, users, logout]);

  const updateLoginScreenConfig = useCallback(async (newConfig: Partial<LoginScreenConfig>) => {
    try {
      const updated = { ...loginScreenConfig, ...newConfig };
      await firebaseUpdateLoginScreenConfig(updated);
      setLoginScreenConfig(updated);
    } catch (error) {
      console.error("Error updating login screen config:", error);
      throw error;
    }
  }, [loginScreenConfig]);

  const sendMessageToAdmin = useCallback(async (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>) => {
    try {
      await createAdminMessage(messageData);
      // Refresh admin messages if current user is admin
      if (isAdmin) {
        const messages = await getAllAdminMessages();
        setAdminMessages(messages);
      }
    } catch (error) {
      console.error("Error sending message to admin:", error);
      throw error;
    }
  }, [isAdmin]);

  const markMessageAsRead = useCallback(async (messageId: string) => {
    try {
      await markAdminMessageAsRead(messageId);
      setAdminMessages(prevMessages =>
        prevMessages.map(message =>
          message.id === messageId ? { ...message, isRead: true } : message
        )
      );
    } catch (error) {
      console.error("Error marking message as read:", error);
      throw error;
    }
  }, []);

  const deleteAdminMessage = useCallback(async (messageId: string) => {
    try {
      await firebaseDeleteAdminMessage(messageId);
      setAdminMessages(prevMessages => prevMessages.filter(message => message.id !== messageId));
    } catch (error) {
      console.error("Error deleting admin message:", error);
      throw error;
    }
  }, []);

  const updateUserProfile = useCallback(async (userId: string, updates: { bio?: string; avatarUrl?: string }) => {
    try {
      await updateUser(userId, updates);

      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId
            ? { ...user, ...updates }
            : user
        )
      );

      // Update current user if it's the same user
      setCurrentUser(prevUser =>
        prevUser && prevUser.id === userId
          ? { ...prevUser, ...updates }
          : prevUser
      );
    } catch (error) {
      console.error("Error updating user profile:", error);
      throw error;
    }
  }, []);

  const contextValue = useMemo(() => ({
    currentUser,
    users,
    isAdmin,
    loading,
    login,
    logout,
    signUpUser,
    approveUser,
    toggleUserActivation,
    removeUser,
    loginScreenConfig,
    updateLoginScreenConfig,
    adminMessages,
    sendMessageToAdmin,
    markMessageAsRead,
    deleteAdminMessage,
    updateUserProfile,
  }), [currentUser, users, isAdmin, loading, login, logout, signUpUser, approveUser, toggleUserActivation, removeUser, loginScreenConfig, updateLoginScreenConfig, adminMessages, sendMessageToAdmin, markMessageAsRead, deleteAdminMessage, updateUserProfile]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
