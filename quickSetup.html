<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Setup - S3Kt0R-Gram</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ff88;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
        }
        h1 {
            text-align: center;
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }
        button {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            background: linear-gradient(45deg, #00cc66, #00aa44);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }
        .result {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            border-color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
        }
        .error {
            border-color: #ff4444;
            background: rgba(255, 68, 68, 0.1);
            color: #ff6666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Quick Firebase Setup</h1>
        <p>This will set up everything in one go!</p>
        
        <button id="setupEverything">⚡ Setup Everything Now</button>
        <div id="result" class="result"></div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, setDoc, addDoc, collection, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        const firebaseConfig = {
            apiKey: "AIzaSyBz12g2Ilty-HnKNCLq4rD9q0bMivjcGUM",
            authDomain: "s3kt0r-30ede.firebaseapp.com",
            projectId: "s3kt0r-30ede",
            storageBucket: "s3kt0r-30ede.firebasestorage.app",
            messagingSenderId: "246065925479",
            appId: "1:246065925479:web:4c7a0f2660fcc9880af3c2",
            measurementId: "G-1R3KWWKXBM"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        document.getElementById('setupEverything').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            const button = document.getElementById('setupEverything');
            
            button.disabled = true;
            resultDiv.textContent = '🔄 Setting up everything...\n';

            try {
                // Step 1: Create/Login Admin
                resultDiv.textContent += '1. Creating admin account...\n';
                let userCredential;
                try {
                    userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'Tr1p$t0p3301');
                    resultDiv.textContent += '✅ Admin already exists, signed in\n';
                } catch (error) {
                    userCredential = await createUserWithEmailAndPassword(auth, '<EMAIL>', 'Tr1p$t0p3301');
                    resultDiv.textContent += '✅ New admin account created\n';
                }

                // Step 2: Create admin user document
                resultDiv.textContent += '2. Setting up admin profile...\n';
                const adminDoc = {
                    id: userCredential.user.uid,
                    username: 'S3Kt0R_Admin',
                    avatarUrl: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=100&h=100&fit=crop&crop=face',
                    isActive: true,
                    isPendingApproval: false,
                    isAdmin: true,
                    bio: 'System Administrator\nThe truth is in the code\nChoose your path wisely\n#S3Kt0R #Truth #Code'
                };
                await setDoc(doc(db, 'users', userCredential.user.uid), adminDoc);
                resultDiv.textContent += '✅ Admin profile created\n';

                // Step 3: Wait for permissions to propagate
                resultDiv.textContent += '3. Waiting for permissions...\n';
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Step 4: Create login config (this should work as it allows public read)
                resultDiv.textContent += '4. Setting up login screen...\n';
                await setDoc(doc(db, 'config', 'loginScreen'), {
                    message: "Welcome to S3Kt0R-Gram. The truth is in the code. Choose your path.",
                    imageUrl: "https://images.unsplash.com/photo-1505682499293-230dd9df9655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
                    imageOverlayOpacity: 0.5
                });
                resultDiv.textContent += '✅ Login screen configured\n';

                // Step 5: Create welcome post
                resultDiv.textContent += '5. Creating welcome post...\n';
                const welcomePost = {
                    userId: userCredential.user.uid,
                    username: 'S3Kt0R_Admin',
                    userAvatarUrl: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=100&h=100&fit=crop&crop=face',
                    imageUrl: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=600&h=400&fit=crop',
                    caption: 'Welcome to S3Kt0R-Gram',
                    contentBody: 'The truth is in the code. Your Firebase setup is complete and ready for action. All systems are operational.',
                    tags: ['welcome', 's3kt0r', 'setup', 'truth'],
                    isAnonymous: false,
                    timestamp: new Date().toISOString(),
                    likes: 0,
                    likedUsers: [],
                    comments: []
                };
                await addDoc(collection(db, 'posts'), welcomePost);
                resultDiv.textContent += '✅ Welcome post created\n';

                // Step 6: Create welcome admin message
                resultDiv.textContent += '6. Creating admin message...\n';
                const adminMessage = {
                    senderName: 'System Setup',
                    senderEmail: '<EMAIL>',
                    subject: 'Firebase Setup Complete',
                    message: 'Your S3Kt0R-Gram Firebase setup has been completed successfully. All collections are initialized and ready for use.',
                    timestamp: new Date().toISOString(),
                    isRead: false
                };
                await addDoc(collection(db, 'adminMessages'), adminMessage);
                resultDiv.textContent += '✅ Admin message created\n';

                resultDiv.className = 'result success';
                resultDiv.textContent += '\n🎉 SETUP COMPLETE!\n';
                resultDiv.textContent += '📧 Admin Email: <EMAIL>\n';
                resultDiv.textContent += '🔑 Admin Password: Tr1p$t0p3301\n';
                resultDiv.textContent += '🆔 Admin UID: ' + userCredential.user.uid + '\n';
                resultDiv.textContent += '\n✅ All Firebase collections initialized\n';
                resultDiv.textContent += '✅ Admin account ready\n';
                resultDiv.textContent += '✅ Posts and messaging systems active\n';
                resultDiv.textContent += '\n🚀 Your S3Kt0R-Gram is ready to use!';

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent += `\n❌ Setup failed: ${error.message}\n`;
                resultDiv.textContent += `Error details: ${error.code || 'Unknown error'}`;
                console.error('Setup error:', error);
            } finally {
                button.disabled = false;
            }
        });
    </script>
</body>
</html>
