<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Setup Admin - S3Kt0R-Gram</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ff88;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 800px;
            width: 100%;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
        }
        h1 {
            text-align: center;
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
            margin-bottom: 30px;
        }
        .step {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            background: linear-gradient(45deg, #00cc66, #00aa44);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .result {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            border-color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
        }
        .error {
            border-color: #ff4444;
            background: rgba(255, 68, 68, 0.1);
            color: #ff6666;
        }
        .loading {
            border-color: #ffaa00;
            background: rgba(255, 170, 0, 0.1);
            color: #ffcc44;
        }
        .credentials {
            background: rgba(0, 255, 136, 0.2);
            border: 2px solid #00ff88;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .credentials h3 {
            margin-top: 0;
            color: #00ff88;
        }
        .cred-item {
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 S3Kt0R-Gram Auto Admin Setup</h1>

        <div class="step">
            <h3>Step 1: Create Admin Account</h3>
            <p>This will create the admin user account and set up all necessary collections.</p>
            <button id="setupAdmin">🚀 Setup Admin Account</button>
            <div id="adminResult" class="result"></div>
        </div>

        <div class="step">
            <h3>Step 2: Initialize Collections</h3>
            <p>This will create all required Firestore collections with proper structure.</p>
            <button id="initCollections">📊 Initialize Collections</button>
            <div id="collectionsResult" class="result"></div>
        </div>

        <div class="step">
            <h3>Step 3: Test Everything</h3>
            <p>Run comprehensive tests to verify everything is working.</p>
            <button id="runTests">🧪 Run Tests</button>
            <div id="testsResult" class="result"></div>
        </div>

        <div class="credentials" id="credentialsBox" style="display: none;">
            <h3>🔑 Admin Credentials</h3>
            <div class="cred-item"><strong>Email:</strong> <EMAIL></div>
            <div class="cred-item"><strong>Password:</strong> Tr1p$t0p3301</div>
            <div class="cred-item"><strong>Status:</strong> Admin with full permissions</div>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, setDoc, collection, getDocs, addDoc, query, orderBy, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        const firebaseConfig = {
            apiKey: "AIzaSyBz12g2Ilty-HnKNCLq4rD9q0bMivjcGUM",
            authDomain: "s3kt0r-30ede.firebaseapp.com",
            projectId: "s3kt0r-30ede",
            storageBucket: "s3kt0r-30ede.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:4c7a0f2660fcc9880af3c2",
            measurementId: "G-1R3KWWKXBM"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        const adminEmail = '<EMAIL>';
        const adminPassword = 'Tr1p$t0p3301';

        // Setup Admin Account
        document.getElementById('setupAdmin').addEventListener('click', async () => {
            const resultDiv = document.getElementById('adminResult');
            const button = document.getElementById('setupAdmin');

            button.disabled = true;
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🔄 Creating admin account...';

            try {
                let userCredential;

                // Try to sign in first (user might already exist)
                try {
                    userCredential = await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
                    resultDiv.textContent += '\n✅ Admin user already exists, signed in successfully';
                } catch (signInError) {
                    // If sign in fails, create new user
                    userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
                    resultDiv.textContent += '\n✅ New admin user created successfully';
                }

                const user = userCredential.user;

                // Create admin user document
                const adminUserDoc = {
                    id: user.uid,
                    username: 'S3Kt0R_Admin',
                    avatarUrl: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=100&h=100&fit=crop&crop=face',
                    isActive: true,
                    isPendingApproval: false,
                    isAdmin: true,
                    bio: 'System Administrator\nThe truth is in the code\nChoose your path wisely\n#S3Kt0R #Truth #Code'
                };

                await setDoc(doc(db, 'users', user.uid), adminUserDoc);
                resultDiv.textContent += '\n✅ Admin user document created in Firestore';

                resultDiv.className = 'result success';
                resultDiv.textContent += '\n\n🎉 Admin account setup complete!';
                resultDiv.textContent += `\n📧 Email: ${adminEmail}`;
                resultDiv.textContent += `\n🆔 UID: ${user.uid}`;

                document.getElementById('credentialsBox').style.display = 'block';

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error setting up admin: ${error.message}`;
            } finally {
                button.disabled = false;
            }
        });

        // Initialize Collections
        document.getElementById('initCollections').addEventListener('click', async () => {
            const resultDiv = document.getElementById('collectionsResult');
            const button = document.getElementById('initCollections');

            button.disabled = true;
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🔄 Initializing collections...';

            try {
                // First ensure we're signed in as admin
                if (!auth.currentUser) {
                    await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
                    resultDiv.textContent += '\n✅ Signed in as admin';
                }

                // Wait a moment for auth to propagate
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Login screen config (public read, admin write)
                await setDoc(doc(db, 'config', 'loginScreen'), {
                    message: "Welcome to S3Kt0R-Gram. The truth is in the code. Choose your path.",
                    imageUrl: "https://images.unsplash.com/photo-1505682499293-230dd9df9655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
                    imageOverlayOpacity: 0.5
                });
                resultDiv.textContent += '\n✅ Login screen config created';

                // Create a real admin message instead of placeholder
                const adminMessageDoc = {
                    id: 'welcome_message',
                    senderName: 'System',
                    senderEmail: '<EMAIL>',
                    subject: 'Welcome to S3Kt0R-Gram',
                    message: 'Firebase setup completed successfully. All systems operational.',
                    timestamp: new Date().toISOString(),
                    isRead: false
                };
                await setDoc(doc(db, 'adminMessages', 'welcome_message'), adminMessageDoc);
                resultDiv.textContent += '\n✅ Admin messages collection initialized';

                // Create a welcome post instead of placeholder
                const welcomePost = {
                    userId: auth.currentUser.uid,
                    username: 'S3Kt0R_Admin',
                    userAvatarUrl: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=100&h=100&fit=crop&crop=face',
                    imageUrl: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=600&h=400&fit=crop',
                    caption: 'Welcome to S3Kt0R-Gram',
                    contentBody: 'The truth is in the code. Your Firebase setup is complete and ready for action.',
                    tags: ['welcome', 's3kt0r', 'setup'],
                    isAnonymous: false,
                    timestamp: new Date().toISOString(),
                    likes: 0,
                    likedUsers: [],
                    comments: []
                };
                await addDoc(collection(db, 'posts'), welcomePost);
                resultDiv.textContent += '\n✅ Posts collection initialized with welcome post';

                resultDiv.className = 'result success';
                resultDiv.textContent += '\n\n🎉 All collections initialized successfully!';

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error initializing collections: ${error.message}`;
                console.error('Collection initialization error:', error);
            } finally {
                button.disabled = false;
            }
        });

        // Run Tests
        document.getElementById('runTests').addEventListener('click', async () => {
            const resultDiv = document.getElementById('testsResult');
            const button = document.getElementById('runTests');

            button.disabled = true;
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🔄 Running comprehensive tests...';

            try {
                // Test 1: Authentication
                const userCredential = await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
                resultDiv.textContent += '\n✅ Authentication test passed';

                // Test 2: Firestore read
                const usersSnapshot = await getDocs(collection(db, 'users'));
                resultDiv.textContent += `\n✅ Firestore read test passed (${usersSnapshot.size} users)`;

                // Test 3: Posts query
                const postsQuery = query(collection(db, 'posts'), orderBy('timestamp', 'desc'));
                const postsSnapshot = await getDocs(postsQuery);
                const posts = postsSnapshot.docs.filter(doc => !doc.data()._placeholder);
                resultDiv.textContent += `\n✅ Posts query test passed (${posts.length} posts)`;

                // Test 4: Create test post
                const testPost = {
                    userId: userCredential.user.uid,
                    username: 'S3Kt0R_Admin',
                    userAvatarUrl: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=100&h=100&fit=crop&crop=face',
                    imageUrl: 'https://picsum.photos/seed/setup-test/400/300',
                    caption: 'Setup test post - Firebase is working!',
                    contentBody: 'This test post confirms that your Firebase setup is working correctly.',
                    tags: ['test', 'setup', 'firebase'],
                    isAnonymous: false,
                    timestamp: Timestamp.now().toDate().toISOString(),
                    likes: 0,
                    likedUsers: [],
                    comments: []
                };

                const docRef = await addDoc(collection(db, 'posts'), testPost);
                resultDiv.textContent += `\n✅ Test post creation passed (ID: ${docRef.id})`;

                resultDiv.className = 'result success';
                resultDiv.textContent += '\n\n🎉 All tests passed! Your Firebase setup is fully functional!';

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent += `\n❌ Test failed: ${error.message}`;
            } finally {
                button.disabled = false;
            }
        });
    </script>
</body>
</html>
