import React from 'react';
import { Link } from 'react-router-dom';
import { APP_NAME } from '../constants';

const PendingApprovalPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-neutral-base flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-neutral-surface/80 backdrop-blur-md p-8 rounded-xl shadow-2xl border border-neutral-border animate-slide-up hover-glow cyber-border">
        <div className="text-center">
          {/* Icon */}
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500/20 border border-yellow-500/30 mb-6 animate-pulse-glow">
            <svg className="h-8 w-8 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>

          {/* Title */}
          <h1 className="text-3xl font-bold text-brand-primary mb-4 animate-text-glow">
            Account Pending Approval
          </h1>

          {/* Message */}
          <div className="space-y-4 text-neutral-200 mb-8">
            <p className="text-lg">
              Welcome to <span className="text-brand-primary font-semibold">{APP_NAME}</span>!
            </p>
            <p>
              Your account has been created successfully and is currently pending admin approval.
            </p>
            <p className="text-sm text-neutral-300">
              You will receive access to the platform once an administrator reviews and approves your account.
              This process helps maintain the security and quality of our community.
            </p>
          </div>

          {/* Status indicator */}
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-6 animate-cyber-flicker">
            <div className="flex items-center justify-center space-x-2">
              <div className="h-2 w-2 bg-yellow-400 rounded-full animate-ping"></div>
              <span className="text-yellow-400 font-medium">Awaiting Admin Review</span>
            </div>
          </div>

          {/* Instructions */}
          <div className="text-sm text-neutral-400 mb-8 space-y-2">
            <p>• Check back later or wait for email notification</p>
            <p>• Contact support if you have questions</p>
            <p>• Do not create multiple accounts</p>
          </div>

          {/* Action buttons */}
          <div className="space-y-3">
            <Link
              to="/login"
              className="w-full bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-3 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary transform hover:scale-105 hover:shadow-lg hover:shadow-brand-primary/25 block text-center hover-glow"
            >
              Try Login Again
            </Link>
            
            <Link
              to="/signup"
              className="w-full bg-neutral-600 hover:bg-neutral-500 text-neutral-200 font-semibold py-3 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-neutral-400 transform hover:scale-105 block text-center"
            >
              Back to Sign Up
            </Link>
          </div>

          {/* Footer note */}
          <div className="mt-8 pt-6 border-t border-neutral-border">
            <p className="text-xs text-neutral-500">
              This is an automated security measure to protect our community.
              Thank you for your patience.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PendingApprovalPage;
